import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/features/order/bloc/order_tracking/order_tracking_bloc.dart';
import 'package:rozana/features/order/bloc/order_tracking/order_tracking_event.dart';
import 'package:rozana/features/order/bloc/order_tracking/order_tracking_state.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';

class OrderTrackingFloatingWidget extends StatelessWidget {
  const OrderTrackingFloatingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderTrackingBloc, OrderTrackingState>(
      builder: (context, state) {
        if (state is OrderTrackingLoaded) {
          final pendingOrders = state.pendingOrders;

          if (pendingOrders.isEmpty) return const SizedBox.shrink();

          // Always show the first pending order
          final orderToShow = pendingOrders.first;

          return _buildFloatingWidget(context, orderToShow);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildFloatingWidget(
    BuildContext context,
    OrderEntity order,
  ) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 10.0),
        child: _buildOrderItem(context, order),
      ),
    );
  }

  Widget _buildOrderItem(
    BuildContext context,
    OrderEntity order,
  ) {
    return GestureDetector(
      onTap: () {
        context.push('${RouteNames.orders}/${order.id}');
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withAlpha(80),
              blurRadius: 6,
              spreadRadius: 1,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        // Using intrinsic width to fit content
        width: null, // Let it size to content
        child: _buildCompactOrderInfo(context, order),
      ),
    );
  }

  Widget _buildCompactOrderInfo(
    BuildContext context,
    OrderEntity order,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: IntrinsicWidth(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Order icon
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppColors.primaryLight.withAlpha(80),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.delivery_dining,
                color: AppColors.surface,
                size: 14,
              ),
            ),
            const SizedBox(width: 8),

            // Order info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Order reference
                  CustomText(
                    color: AppColors.surface,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    'Order #${order.id}',
                  ),
                ],
              ),
            ),
            // Close button
            GestureDetector(
              onTap: () {
                context.read<OrderTrackingBloc>().add(
                      HideOrderTrackingWidget(),
                    );
              },
              child: Container(
                margin: const EdgeInsets.only(left: 16),
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.primaryLight.withAlpha(40),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: AppColors.surface,
                  size: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
