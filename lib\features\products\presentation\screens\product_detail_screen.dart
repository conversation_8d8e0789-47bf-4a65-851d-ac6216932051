import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/domain/entities/product_entity.dart';

import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/screens/cart_screen.dart';
import 'package:rozana/features/products/bloc/product_listing_bloc.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/services/appflyer_services/app_flyer_deeplink.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/cart_item_model.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/utils/cart_utils.dart';
import '../../bloc/product_listing_state.dart';
import '../../services/product_navigation_service.dart';
import '../widgets/product_section.dart';
import '../widgets/product_image_slider.dart';

class ProductDetailPage extends StatelessWidget {
  const ProductDetailPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        ProductNavigationService.instance.pop();
      },
      child: BlocBuilder<ProductListingBloc, ProductListingState>(
        buildWhen: (previous, current) {
          bool isPreviousData = previous.maybeMap(
              productBySkuLoading: (value) => true,
              productBySkuLoaded: (value) => true,
              orElse: () => false);

          bool isCurrentData = previous.maybeMap(
              productBySkuLoading: (value) => true,
              productBySkuLoaded: (value) => true,
              orElse: () => false);

          return isPreviousData && isCurrentData;
        },
        builder: (context, state) {
          bool isLoading = true;
          ProductEntity? productData;
          state.maybeMap(
            productBySkuLoaded: (value) {
              isLoading = false;
              productData = value.product;
            },
            orElse: () {
              isLoading = true;
            },
          );

          final String name = productData?.name ?? '--';
          final String productId = productData?.id ?? '';
          final String sku = productData?.skuID ?? '';
          final String description =
              productData?.description ?? 'No description available';
          final String imageUrl =
              // (productData?.photos?.isNotEmpty ?? false)
              //     ? productData!.photos![0]
              //     :
              productData?.imageUrl ?? '';
          final List<String> photos = (productData?.photos?.isNotEmpty ?? false)
              ? productData!.photos!
              : [imageUrl].where((url) => url.isNotEmpty).toList();
          final double price = productData?.price.toDouble() ?? 0.0;
          final double originalPrice =
              productData?.originalPrice?.toDouble() ?? price;
          final double rating = productData?.rating.toDouble() ?? 0.0;
          final int reviewCount = productData?.reviewCount.toInt() ?? 0;
          final int discountPercentage =
              productData?.discountPercentage.toInt() ?? 0;
          // final bool isFeatured = widget.productData['isFeatured'] ?? false;
          final num availableQty = productData?.availableQty ?? 0;
          final num maxQty = productData?.maxLimit ?? 0;
          final String? variantName = productData?.variantName;
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 1,
              foregroundColor: Colors.black,
              title: isLoading
                  ? ShimmerText(height: 14, width: 100)
                  : Text(name, style: const TextStyle(color: Colors.black)),
              actions: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: isLoading
                      ? Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: ShimmerBox(height: 30, width: 30),
                        )
                      : IconButton(
                          icon: const Icon(Icons.share),
                          onPressed: () {
                            _createAndShareDeepLink(productId, name, imageUrl,
                                description, sku, variantName);
                          }),
                ),
              ],
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        isLoading
                            ? AspectRatio(
                                aspectRatio: 1,
                                child: ShimmerBox(
                                  width: double.infinity,
                                ))
                            : ProductImageSlider(
                                images: photos,
                                aspectRatio: 1,
                                showIndicators: true,
                              ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  isLoading
                                      ? ShimmerText(
                                          height: 22,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.6,
                                        )
                                      : Text(
                                          name,
                                          style: const TextStyle(
                                              fontSize: 22,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black87),
                                        ),
                                  if (variantName != null &&
                                      variantName.isNotEmpty) ...[
                                    const SizedBox(height: 4),
                                    Text(
                                      variantName,
                                      style: const TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ],
                                  if (isLoading)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: ShimmerText(
                                        height: 14,
                                        width: 40,
                                      ),
                                    )
                                ],
                              ),
                            ),
                            if (discountPercentage > 0)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.green[50],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '-$discountPercentage%',
                                  style: const TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            if (isLoading)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                child: ShimmerBox(
                                  radius: 12,
                                  width: 50,
                                  height: 30,
                                ),
                              )
                          ],
                        ),
                        const SizedBox(height: 10),

                        Row(
                          children: [
                            isLoading
                                ? ShimmerText(
                                    height: 20,
                                    width:
                                        MediaQuery.of(context).size.width * 0.2,
                                  )
                                : Text(
                                    '₹${price.round()}',
                                    style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                    ),
                                  ),
                            const SizedBox(width: 10),
                            if (originalPrice > price)
                              Text(
                                '₹${originalPrice.round()}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            const SizedBox(
                              width: 10,
                            ),
                            if (originalPrice > price) ...[
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.blue,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${(100 - (price / originalPrice * 100)).round()}% off',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: AppColors.background,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ]
                          ],
                        ),
                        const SizedBox(height: 10),

                        // Ratings
                        isLoading
                            ? ShimmerText(
                                height: 14,
                                width: MediaQuery.of(context).size.width * 0.5,
                              )
                            : Row(
                                children: [
                                  ...List.generate(5, (index) {
                                    if (index < rating.floor()) {
                                      return const Icon(Icons.star,
                                          size: 18, color: Colors.amber);
                                    } else {
                                      return const Icon(Icons.star_border,
                                          size: 18, color: Colors.grey);
                                    }
                                  }),
                                  const SizedBox(width: 8),
                                  Text('$rating ($reviewCount reviews)',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.black26,
                                      )),
                                ],
                              ),
                        const SizedBox(height: 20),
                        if (description.isNotEmpty) ...[
                          // Description
                          isLoading
                              ? ShimmerText(
                                  height: 18,
                                  width:
                                      MediaQuery.of(context).size.width * 0.4,
                                )
                              : const Text(
                                  "Product Description",
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black87),
                                ),
                          const SizedBox(height: 8),
                          isLoading
                              ? ShimmerText(
                                  height: 15,
                                  width:
                                      MediaQuery.of(context).size.width * 0.6,
                                )
                              : Text(
                                  description,
                                  style: const TextStyle(
                                      fontSize: 15,
                                      height: 1.5,
                                      color: Colors.black54),
                                ),
                        ],
                        // Similar Products Section
                        if ((!isLoading) &&
                            productData?.categoryId?.toString().isNotEmpty ==
                                true)
                          Padding(
                            padding: const EdgeInsets.only(top: 32),
                            child: ProductsSection.category(
                              categoryId:
                                  productData?.categoryId.toString() ?? '',
                              title: 'View Similar',
                              height: 250,
                              showSeeAll:
                                  false, // Don't show see all for similar products
                              excludeProductId: productData?.id.toString(),
                            ),
                          ),
                        if ((!isLoading) &&
                            productData?.brandId?.toString().isNotEmpty == true)
                          Padding(
                            padding: const EdgeInsets.only(top: 32),
                            child: ProductsSection.brand(
                              brandId: productData?.brandId.toString() ?? '',
                              title: 'More from ${productData?.brandName}',
                              height: 250,
                              showSeeAll:
                                  false, // Don't show see all for similar products
                              excludeProductId: productData?.id.toString(),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

                // Bottom Buttons
                SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    child: isLoading
                        ? ShimmerBox(height: 48)
                        : BlocBuilder<CartBloc, CartState>(
                            builder: (context, state) {
                              final quantity = CartUtils.getItemQuantity(
                                  productId, sku, state.cart);
                              final totalItemsInCart = state.cart.totalItems;

                              return Row(
                                children: [
                                  // View Cart button - only show if there are other items in cart
                                  if (totalItemsInCart > 0) ...[
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () {
                                          HapticFeedback.lightImpact();
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      CartScreen()));
                                        },
                                        child: Container(
                                          height: 48,
                                          decoration: BoxDecoration(
                                            color: Colors.transparent,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                              color: AppColors.primary,
                                              width: 1,
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              // Cart icon with item count badge - exact same as floating cart button
                                              Stack(
                                                clipBehavior: Clip.none,
                                                children: [
                                                  const Icon(
                                                    Icons
                                                        .shopping_cart_outlined,
                                                    color: AppColors.primary,
                                                    size: 22,
                                                  ),
                                                  Positioned(
                                                    top: -8,
                                                    right: -8,
                                                    child: Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              4),
                                                      decoration:
                                                          const BoxDecoration(
                                                        color:
                                                            AppColors.primary,
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: Text(
                                                        '${state.cart.totalItems}',
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 10,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(width: 12),
                                              const Text(
                                                "View Cart",
                                                style: TextStyle(
                                                  color: AppColors.primary,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                  ],

                                  // Add to Cart / Quantity Selector
                                  Expanded(
                                    child: quantity > 0
                                        ? _buildQuantitySelector(
                                            productId,
                                            sku,
                                            name,
                                            imageUrl,
                                            originalPrice,
                                            price,
                                            availableQty,
                                            maxQty,
                                            productData)
                                        : GestureDetector(
                                            onTap: () {
                                              HapticFeedback.lightImpact();

                                              context
                                                  .read<CartBloc>()
                                                  .add(CartEvent.addItem(
                                                      item: CartItemModel(
                                                    productId: productId,
                                                    name: name,
                                                    price: originalPrice,
                                                    imageUrl: imageUrl,
                                                    quantity: 1,
                                                    unit:
                                                        'item', // Default unit, can be customized
                                                    discountedPrice: price,
                                                    facilityId: productData
                                                        ?.facilityId
                                                        ?.toString(),
                                                    facilityName: productData
                                                        ?.facilityName
                                                        ?.toString(),
                                                    skuID: productData?.skuID
                                                        ?.toString(),
                                                    availableQuantity:
                                                        availableQty,
                                                    maxQuantity: maxQty,
                                                    tax: productData?.tax,
                                                    cgst: productData?.cgst,
                                                    sgst: productData?.sgst,
                                                  )));
                                            },
                                            child: Container(
                                              height: 48,
                                              decoration: BoxDecoration(
                                                color: Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: AppColors.primary,
                                                  width: 1,
                                                ),
                                              ),
                                              alignment: Alignment.center,
                                              child: const Text(
                                                "Add to Cart",
                                                style: TextStyle(
                                                    color: AppColors.primary,
                                                    fontSize: 14,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                            ),
                                          ),
                                  ),
                                ],
                              );
                            },
                          ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuantitySelector(
      String productId,
      String sku,
      String name,
      String imageUrl,
      double originalPrice,
      double discountedPrice,
      num availableQty,
      num maxQty,
      ProductEntity? product) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        // final quantity = CartUtils.getItemQuantity(productId, state.cart);
        final quantity = CartUtils.getItemQuantity(productId, sku, state.cart);

        return Container(
          height: 48,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.primary,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Minus button
              InkWell(
                splashColor: Colors.transparent,
                onTap: () async {
                  HapticFeedback.lightImpact();
                  final cartItemId =
                      CartUtils.getCartItemId(productId, sku, state.cart);

                  if (cartItemId != null) {
                    if (quantity > 1) {
                      context.read<CartBloc>().add(CartEvent.updateQuantity(
                          cartItemId, sku, (quantity - 1).toInt()));
                    } else if (quantity == 1) {
                      context
                          .read<CartBloc>()
                          .add(CartEvent.removeItem(cartItemId, sku));
                    }
                  }
                },
                borderRadius:
                    const BorderRadius.horizontal(left: Radius.circular(8)),
                child: Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.remove,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              ),

              // Quantity display
              Container(
                alignment: Alignment.center,
                width: 28,
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),

              // Plus button
              InkWell(
                splashColor: Colors.transparent,
                onTap: () async {
                  HapticFeedback.lightImpact();
                  if (quantity == 0) {
                    context.read<CartBloc>().add(CartEvent.addItem(
                        item: CartItemModel(
                            productId: productId,
                            name: name,
                            price: originalPrice,
                            imageUrl: imageUrl,
                            quantity: 1,
                            unit: 'item', // Default unit, can be customized
                            discountedPrice: discountedPrice,
                            facilityId: product?.facilityId?.toString(),
                            facilityName: product?.facilityName?.toString(),
                            skuID: product?.skuID?.toString(),
                            availableQuantity: availableQty,
                            maxQuantity: maxQty,
                            tax: product?.tax,
                            cgst: product?.cgst,
                            sgst: product?.sgst)));
                  } else {
                    final cartItemId =
                        CartUtils.getCartItemId(productId, sku, state.cart);
                    if (cartItemId != null) {
                      context.read<CartBloc>().add(CartEvent.updateQuantity(
                          cartItemId, sku, (quantity + 1).toInt()));
                    }
                  }
                  // widget.onAddToCart?.call();
                  // Force rebuild after async operation
                  // if (mounted) {
                  //   setState(() {});
                  // }
                },
                borderRadius:
                    const BorderRadius.horizontal(right: Radius.circular(8)),
                child: Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.add,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Generates a Branch deep link and opens the native share sheet.
Future<void> _createAndShareDeepLink(
    String productId,
    String name,
    String imageUrl,
    String description,
    String sku,
    String? variantName) async {
  AppFlyerDeeplink.createDeepLink(
    data: {
      'screen': RouteNames.productDetail,
      'sku': sku,
      'variantName': variantName ?? ''
    },
    name: name,
  );
}
