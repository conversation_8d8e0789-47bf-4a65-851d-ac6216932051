import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../domain/entities/product_entity.dart';
import '../../../products/presentation/widgets/product_card.dart';
import '../../bloc/home bloc/home_bloc.dart';

class MostBoughtSection extends StatelessWidget {
  const MostBoughtSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.mostBought != current.mostBought;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? mostBought =
            state.mapOrNull(loaded: (value) => value.mostBought);
        // Use AnimatedSwitcher for smooth transitions between loading and loaded states
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          switchInCurve: Curves.easeInOut,
          switchOutCurve: Curves.easeInOut,
          child: mostBought == null
              ? RepaintBoundary(
                  child: Semantics(
                    label: 'Loading top products',
                    hint: 'Please wait while top products are loading',
                    child: Column(
                      key: const ValueKey('most_bought_skeleton'),
                      children: [
                        // Use enhanced shimmer with staggered loading for better visual feedback
                        ProductGrid2(
                          productList: null,
                          useEnhancedShimmer: true,
                        ),
                      ],
                    ),
                  ),
                )
              : mostBought.isEmpty
                  ? const SizedBox
                      .shrink() // Don't show anything if list is empty
                  : RepaintBoundary(
                      child: Column(
                        key: const ValueKey('most_bought_loaded'),
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: AppDimensions.screenHzPadding),
                            child: Row(
                              children: [
                                Expanded(
                                  child: CustomText(
                                    'Frequently bought',
                                    fontSize: 20,
                                    fontWeight: FontWeight.w900,
                                    color: AppColors.primary700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          ProductGrid2(productList: mostBought),
                        ],
                      ),
                    ),
        );
      },
    );
  }
}

class ProductGrid2 extends StatelessWidget {
  const ProductGrid2({
    super.key,
    required this.productList,
    this.useEnhancedShimmer = true,
  });

  final List<ProductEntity>? productList;
  final bool useEnhancedShimmer;

  @override
  Widget build(BuildContext context) {
    final double itemWidth =
        (MediaQuery.of(context).size.width / 3) - AppDimensions.screenHzPadding;
    // Show shimmer loading state when productList is null
    if (productList == null) {
      return RepaintBoundary(
        child: Semantics(
          label: 'Loading products',
          hint: 'Please wait while products are loading',
          child: GridView.builder(
            shrinkWrap: true,
            primary: false,
            padding:
                EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
            itemCount: 6,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.65,
              mainAxisSpacing: 20,
              crossAxisSpacing: 10,
            ),
            itemBuilder: (ctx, index) {
              return ProductCardShimmer();
            },
          ),
        ),
      );
    }

    int totalLength = productList?.length ?? 0;

    final int crossAxisCount = (totalLength <= 6) ? 1 : 2;

    return RepaintBoundary(
      child: Semantics(
        label: 'Product grid',
        hint: '${productList!.length} products available',
        child: SizedBox(
          height: (crossAxisCount == 1) ? 220 : (220 * 2) + 8,
          child: GridView.builder(
            padding:
                EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
            scrollDirection: Axis.horizontal,
            itemCount: totalLength,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              childAspectRatio: 200 / itemWidth,
            ),
            itemBuilder: (context, index) {
              return DiscountedProductCard(
                product: productList![index],
                isLoading: false,
              );
            },
          ),
        ),
      ),
    );
  }
}
