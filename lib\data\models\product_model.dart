class ProductModel {
  String? id;
  String? objectID;
  String? name;
  String? description;
  String? imageUrl;
  List<String>? photos;
  num? price;
  num? originalPrice;
  num? rating;
  num? reviewCount;
  bool? isInWishlist;
  bool? isOutOfStock;
  String? discountLabel;
  String? category;
  String? categoryId;
  String? subcategory;
  String? facilityId;
  String? facilityName;
  String? skuID;
  String? brandId;
  String? brandName;
  num? availableQty;
  num? maxLimit;
  String? variantName;
  String? parentSku;
  String? childSku;
  List<ProductModel>? variants;
  int? totalVariants;
  num? tax;
  num? cgst;
  num? sgst;
  bool? taxable;

  ProductModel(
      {this.id,
      this.objectID,
      this.name,
      this.description,
      this.imageUrl,
      this.photos,
      this.price,
      this.originalPrice,
      this.rating,
      this.reviewCount,
      this.isInWishlist,
      this.isOutOfStock,
      this.discountLabel,
      this.category,
      this.categoryId,
      this.subcategory,
      this.facilityId,
      this.facilityName,
      this.skuID,
      this.brandId,
      this.brandName,
      this.availableQty,
      this.maxLimit,
      this.variantName,
      this.parentSku,
      this.childSku,
      this.variants,
      this.totalVariants,
      this.tax,
      this.cgst,
      this.sgst,
      this.taxable});

  ProductModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    objectID = json['objectID']?.toString();
    name = json['name']?.toString();
    description = json['description']?.toString();
    imageUrl = json['imageUrl']?.toString();
    photos = json['photos'] is List ? List<String>.from(json['photos']) : null;
    categoryId = json['categoryId']?.toString();
    if (json['price'] is num) {
      price = json['price'];
    } else if (json['price'] != null) {
      var numb = num.tryParse(json['price']!.toString());
      if (numb is num) {
        price = numb;
      }
    }
    if (json['originalPrice'] is num) {
      originalPrice = json['originalPrice'];
    } else if (json['originalPrice'] != null) {
      var numb = num.tryParse(json['originalPrice']!.toString());
      if (numb is num) {
        originalPrice = numb;
      }
    }
    if (json['rating'] is num) {
      rating = json['rating'];
    } else if (json['rating'] != null) {
      var numb = num.tryParse(json['rating']!.toString());
      if (numb is num) {
        rating = numb;
      }
    }
    if (json['reviewCount'] is num) {
      reviewCount = json['reviewCount'];
    } else if (json['reviewCount'] != null) {
      var numb = num.tryParse(json['reviewCount']!.toString());
      if (numb is num) {
        reviewCount = numb;
      }
    }
    if (json['isInWishlist'] is bool) {
      isInWishlist = json['isInWishlist'];
    }
    if (json['isOutOfStock'] is bool) {
      isOutOfStock = json['isOutOfStock'];
    }
    discountLabel = json['discountLabel']?.toString();
    category = json['category']?.toString();
    subcategory = json['subcategory']?.toString();
    // Try both camelCase and snake_case for backward compatibility
    facilityId =
        json['facilityId']?.toString() ?? json['facility_id']?.toString();
    facilityName =
        json['facilityName']?.toString() ?? json['facility_name']?.toString();
    skuID = json['sku']?.toString() ??
        json['sku_id']?.toString() ??
        json['skuid']?.toString();
    // Try both camelCase and snake_case for backward compatibility
    brandId = json['brandId']?.toString() ?? json['brand_id']?.toString();
    brandName = json['brandName']?.toString() ?? json['brand_name']?.toString();

    if (json['available_qty'] is num) {
      availableQty = json['available_qty'];
    } else if (json['available_qty'] != null) {
      var numb = num.tryParse(json['available_qty']!.toString());
      if (numb is num) {
        availableQty = numb;
      }
    }

    if (json['max_purchase_limit'] is num) {
      maxLimit = json['max_purchase_limit'];
    } else if (json['max_purchase_limit'] != null) {
      var numb = num.tryParse(json['max_purchase_limit']!.toString());
      if (numb is num) {
        maxLimit = numb;
      }
    }
    parentSku = json['parent_sku']?.toString();
    childSku = json['child_sku']?.toString();
    variantName = json['variant_name']?.toString();

    // Parse variants if available
    if (json['variants'] is List) {
      variants = <ProductModel>[];
      for (var variant in json['variants']) {
        if (variant is Map<String, dynamic>) {
          variants!.add(ProductModel.fromJson(variant));
        }
      }
    }

    if (json['tax'] is num) {
      tax = json['tax'];
    } else if (json['tax'] != null) {
      var numb = num.tryParse(json['tax']!.toString());
      if (numb is num) {
        tax = numb;
      } else {
        tax = 0;
      }
    }

    if (json['cgst'] is num) {
      cgst = json['cgst'];
    } else if (json['cgst'] != null) {
      var numb = num.tryParse(json['cgst']!.toString());
      if (numb is num) {
        cgst = numb;
      } else {
        cgst = 0;
      }
    }

    if (json['sgst'] is num) {
      sgst = json['sgst'];
    } else if (json['sgst'] != null) {
      var numb = num.tryParse(json['sgst']!.toString());
      if (numb is num) {
        sgst = numb;
      } else {
        sgst = 0;
      }
    }

    taxable = json['taxable'] ?? false;
    totalVariants = json['totalVariants']?.toInt();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id is String) {
      data['id'] = id;
    }
    if (objectID is String) {
      data['objectID'] = objectID;
    }
    if (name is String) {
      data['name'] = name;
    }
    if (description is String) {
      data['description'] = description;
    }
    if (imageUrl is String) {
      data['imageUrl'] = imageUrl;
    }
    if (photos != null) {
      data['photos'] = photos;
    }
    if (price is num) {
      data['price'] = price;
    } else if (price != null) {
      var numb = num.tryParse(price.toString());
      if (numb is num) {
        data['price'] = numb;
      }
    }
    if (originalPrice is num) {
      data['originalPrice'] = originalPrice;
    } else if (originalPrice != null) {
      var numb = num.tryParse(originalPrice.toString());
      if (numb is num) {
        data['originalPrice'] = numb;
      }
    }
    if (rating is num) {
      data['rating'] = rating;
    } else if (rating != null) {
      var numb = num.tryParse(rating.toString());
      if (numb is num) {
        data['rating'] = numb;
      }
    }
    if (reviewCount is num) {
      data['reviewCount'] = reviewCount;
    } else if (reviewCount != null) {
      var numb = num.tryParse(reviewCount.toString());
      if (numb is num) {
        data['reviewCount'] = numb;
      }
    }
    if (isInWishlist is bool) {
      data['isInWishlist'] = isInWishlist;
    }
    if (isOutOfStock is bool) {
      data['isOutOfStock'] = isOutOfStock;
    }
    if (discountLabel is String) {
      data['discountLabel'] = discountLabel;
    }
    if (category is String) {
      data['category'] = category;
    }
    if (categoryId is String) {
      data['categoryId'] = categoryId;
    }
    if (subcategory is String) {
      data['subcategory'] = subcategory;
    }
    if (facilityId is String) {
      data['facilityId'] = facilityId;
    }
    if (facilityName is String) {
      data['facilityName'] = facilityName;
    }
    if (skuID is String) {
      data['sku'] = skuID;
    }
    if (brandId is String) {
      data['brandId'] = brandId;
    }
    if (brandName is String) {
      data['brandName'] = brandName;
    }
    if (availableQty is num) {
      data['available_qty'] = availableQty;
    } else if (availableQty != null) {
      var numb = num.tryParse(availableQty.toString());
      if (numb is num) {
        data['available_qty'] = numb;
      }
    }
    if (maxLimit is num) {
      data['max_purchase_limit'] = maxLimit;
    } else if (maxLimit != null) {
      var numb = num.tryParse(maxLimit.toString());
      if (numb is num) {
        data['max_purchase_limit'] = numb;
      }
    }
    if (variantName is String) {
      data['variant_name'] = variantName;
    }
    if (parentSku is String) {
      data['parent_sku'] = parentSku;
    }
    if (childSku is String) {
      data['child_sku'] = childSku;
    }

    // Add variants list if available
    if (variants != null && variants!.isNotEmpty) {
      data['variants'] = variants!.map((variant) => variant.toJson()).toList();
    }
    data['totalVariants'] = totalVariants;
    if (tax is num) {
      data['tax'] = tax;
    } else if (tax != null) {
      var numb = num.tryParse(tax.toString());
      if (numb is num) {
        data['tax'] = numb;
      } else {
        data['tax'] = 0;
      }
    }
    if (cgst is num) {
      data['cgst'] = cgst;
    } else if (cgst != null) {
      var numb = num.tryParse(cgst.toString());
      if (numb is num) {
        data['cgst'] = numb;
      } else {
        data['cgst'] = 0;
      }
    }
    if (sgst is num) {
      data['sgst'] = sgst;
    } else if (sgst != null) {
      var numb = num.tryParse(sgst.toString());
      if (numb is num) {
        data['sgst'] = numb;
      } else {
        data['sgst'] = 0;
      }
    }
    data['taxable'] = taxable ?? false;
    return data;
  }
}

class ProductComboModel {
  String? id;
  String? objectID;
  String? name;
  String? description;
  num? price;
  num? originalPrice;
  String? discountLabel;
  String? category;
  String? subcategory;
  List<ProductModel>? products;
  String? facilityId;
  String? facilityName;
  String? skuID;
  String? brandId;
  String? brandName;
  String? parentSku;
  String? childSku;
  int? totalVariants;
  num? tax;
  num? cgst;
  num? sgst;
  bool? taxable;

  ProductComboModel(
      {this.id,
      this.objectID,
      this.name,
      this.description,
      this.price,
      this.originalPrice,
      this.discountLabel,
      this.category,
      this.subcategory,
      this.products,
      this.facilityId,
      this.facilityName,
      this.skuID,
      this.brandId,
      this.brandName,
      this.parentSku,
      this.childSku,
      this.totalVariants,
      this.tax,
      this.cgst,
      this.sgst,
      this.taxable});

  ProductComboModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    objectID = json['objectID']?.toString();
    name = json['name']?.toString();
    description = json['description']?.toString();
    if (json['price'] is num) {
      price = json['price'];
    } else if (json['price'] != null) {
      var numb = num.tryParse(json['price']!.toString());
      if (numb is num) {
        price = numb;
      }
    }
    if (json['originalPrice'] is num) {
      originalPrice = json['originalPrice'];
    } else if (json['originalPrice'] != null) {
      var numb = num.tryParse(json['originalPrice']!.toString());
      if (numb is num) {
        originalPrice = numb;
      }
    }
    discountLabel = json['discountLabel']?.toString();
    category = json['category']?.toString();
    subcategory = json['subcategory']?.toString();
    if (json['products'] is List) {
      products = <ProductModel>[];
      json['products'].forEach((v) {
        if (v is Map<String, dynamic>) {
          products!.add(ProductModel.fromJson(v));
        }
      });
    }
    if (json['facilityId'] is String) {
      facilityId = json['facilityId']?.toString();
    }
    if (json['facilityName'] is String) {
      facilityName = json['facilityName']?.toString();
    }
    if (json['sku'] is String) {
      skuID = json['sku']?.toString();
    }
    // Try both camelCase and snake_case for backward compatibility
    brandId = json['brandId']?.toString() ?? json['brand_id']?.toString();
    brandName = json['brandName']?.toString() ?? json['brand_name']?.toString();
    parentSku = json['parent_sku']?.toString();
    childSku = json['child_sku']?.toString();
    totalVariants = json['totalVariants']?.toInt();
    if (json['tax'] is num) {
      tax = json['tax'];
    } else if (json['tax'] != null) {
      var numb = num.tryParse(json['tax']!.toString());
      if (numb is num) {
        tax = numb;
      } else {
        tax = 0;
      }
    }
    if (json['cgst'] is num) {
      cgst = json['cgst'];
    } else if (json['cgst'] != null) {
      var numb = num.tryParse(json['cgst']!.toString());
      if (numb is num) {
        cgst = numb;
      } else {
        cgst = 0;
      }
    }
    if (json['sgst'] is num) {
      sgst = json['sgst'].toDouble();
    } else if (json['sgst'] != null) {
      var numb = num.tryParse(json['sgst']!.toString());
      if (numb is num) {
        sgst = numb.toDouble();
      } else {
        sgst = 0;
      }
    }
    taxable = json['taxable'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id is String) {
      data['id'] = id;
    }
    if (objectID is String) {
      data['objectID'] = objectID;
    }
    if (name is String) {
      data['name'] = name;
    }
    if (description is String) {
      data['description'] = description;
    }
    if (price is num) {
      data['price'] = price;
    } else if (price != null) {
      var numb = num.tryParse(price.toString());
      if (numb is num) {
        data['price'] = numb;
      }
    }
    if (originalPrice is num) {
      data['originalPrice'] = originalPrice;
    } else if (originalPrice != null) {
      var numb = num.tryParse(originalPrice.toString());
      if (numb is num) {
        data['originalPrice'] = numb;
      }
    }
    if (discountLabel is String) {
      data['discountLabel'] = discountLabel;
    }
    if (category is String) {
      data['category'] = category;
    }
    if (subcategory is String) {
      data['subcategory'] = subcategory;
    }
    if (products != null) {
      data['products'] = products!.map((item) => item.toJson()).toList();
    } else {
      data['items'] = [];
    }
    if (facilityId is String) {
      data['facilityId'] = facilityId;
    }
    if (facilityName is String) {
      data['facilityName'] = facilityName;
    }
    if (skuID is String) {
      data['sku'] = skuID;
    }
    if (brandId is String) {
      data['brandId'] = brandId;
    }
    if (brandName is String) {
      data['brandName'] = brandName;
    }
    if (parentSku is String) {
      data['parent_sku'] = parentSku;
    }
    if (childSku is String) {
      data['child_sku'] = childSku;
    }
    data['totalVariants'] = totalVariants;
    if (tax is num) {
      data['tax'] = tax;
    } else if (tax != null) {
      var numb = num.tryParse(tax.toString());
      if (numb is num) {
        data['tax'] = numb;
      } else {
        data['tax'] = 0;
      }
    }
    if (cgst is num) {
      data['cgst'] = cgst;
    } else if (cgst != null) {
      var numb = num.tryParse(cgst.toString());
      if (numb is num) {
        data['cgst'] = numb;
      } else {
        data['cgst'] = 0;
      }
    }
    if (sgst is num) {
      data['sgst'] = sgst;
    } else if (sgst != null) {
      var numb = num.tryParse(sgst.toString());
      if (numb is num) {
        data['sgst'] = numb;
      } else {
        data['sgst'] = 0;
      }
    }
    data['taxable'] = taxable ?? false;
    return data;
  }
}
