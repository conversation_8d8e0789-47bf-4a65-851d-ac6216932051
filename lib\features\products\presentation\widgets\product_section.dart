import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import '../../../../data/mappers/product_mapper.dart';

import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../widgets/lazy_loading_widget.dart';
import '../../../../widgets/shimmer_widgets.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../bloc/product_listing_bloc.dart';
import '../../bloc/product_listing_event.dart';
import '../../bloc/product_listing_state.dart';
import 'product_skeleton_loader.dart';

/// Type of products section to determine which API to call
enum ProductsSectionType { category, brand }

/// A unified reusable widget that displays products in various configurations
/// Supports category products, brand products, featured products, external products, and search results
/// Can display in horizontal list or grid view with lazy loading
class ProductsSection extends StatefulWidget {
  // Core properties
  final VoidCallback? onSeeAllTap;
  final String title;
  final TextStyle? titleStyle;
  final String rightActionText;
  final TextStyle? rightActionTextStyle;
  final double height;
  final bool showSeeAll;
  final bool? shrinkWrap;

  // Data loading configuration
  final double? cardWidth;
  final bool preloadData;
  final CategoryEntity?
      category; // Using CategoryEntity instead of separate IDs
  final bool featured;
  final Function(Map<String, dynamic>)? onProductTap;
  final List<ProductModel>?
      externalProducts; // External products for search results
  final VoidCallback? onLoadMore; // Callback for loading more products

  // Category/Brand specific properties (from products_section.dart)
  final String? id; // Can be categoryId or brandId
  final ProductsSectionType? type; // Category or brand type
  final String? excludeProductId; // Product ID to exclude from results

  // Grid/List view configuration
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final double imageWidth;
  final double imageHeight;
  final ScrollController? scrollController;
  final bool showEmptyText;
  final double? topPadding;
  final bool useParentId;
  final double bottomPadding;
  final bool? primary;
  final ScrollPhysics? physics;
  final bool useCollectionId;

  const ProductsSection({
    super.key,
    this.onSeeAllTap,
    this.height = 200,
    this.imageWidth = 160,
    this.imageHeight = 160,
    this.title = 'Products',
    this.titleStyle,
    this.rightActionText = 'See All',
    this.rightActionTextStyle,
    this.preloadData = true,
    this.category,
    this.featured = false,
    this.useGridView = false,
    this.gridCrossAxisCount = 2,
    this.gridChildAspectRatio = 0.7,
    this.showSeeAll = true,
    this.onProductTap,
    this.scrollController,
    this.externalProducts,
    this.onLoadMore,
    this.id,
    this.type,
    this.cardWidth,
    this.showEmptyText = true,
    this.topPadding,
    this.useParentId = false,
    this.excludeProductId,
    this.bottomPadding = 0,
    this.shrinkWrap,
    this.primary,
    this.physics,
    this.useCollectionId = false,
  });

  /// Factory constructor for category products (from products_section.dart)
  const ProductsSection.category({
    super.key,
    required String categoryId,
    required String title,
    double height = 200,
    VoidCallback? onSeeAllTap,
    bool showSeeAll = true,
    TextStyle? titleStyle,
    String rightActionText = 'See All',
    TextStyle? rightActionTextStyle,
    this.cardWidth,
    this.showEmptyText = true,
    this.topPadding,
    this.useParentId = false,
    this.excludeProductId,
    this.bottomPadding = 0,
    this.shrinkWrap,
    this.primary,
    this.physics,
    this.useCollectionId = false,
  })  : id = categoryId,
        type = ProductsSectionType.category,
        title = title,
        height = height,
        onSeeAllTap = onSeeAllTap,
        showSeeAll = showSeeAll,
        titleStyle = titleStyle,
        rightActionText = rightActionText,
        rightActionTextStyle = rightActionTextStyle,
        preloadData = true,
        category = null,
        featured = false,
        useGridView = false,
        gridCrossAxisCount = 2,
        gridChildAspectRatio = 0.7,
        imageWidth = 160,
        imageHeight = 160,
        onProductTap = null,
        scrollController = null,
        externalProducts = null,
        onLoadMore = null;

  /// Factory constructor for brand products (from products_section.dart)
  const ProductsSection.brand({
    super.key,
    required String brandId,
    required String title,
    double height = 200,
    VoidCallback? onSeeAllTap,
    bool showSeeAll = true,
    TextStyle? titleStyle,
    String rightActionText = 'See All',
    TextStyle? rightActionTextStyle,
    this.cardWidth,
    this.showEmptyText = true,
    this.topPadding,
    this.useParentId = false,
    this.excludeProductId,
    this.bottomPadding = 0,
    this.shrinkWrap,
    this.primary,
    this.physics,
    this.useCollectionId = false,
  })  : id = brandId,
        type = ProductsSectionType.brand,
        title = title,
        height = height,
        onSeeAllTap = onSeeAllTap,
        showSeeAll = showSeeAll,
        titleStyle = titleStyle,
        rightActionText = rightActionText,
        rightActionTextStyle = rightActionTextStyle,
        preloadData = true,
        category = null,
        featured = false,
        useGridView = false,
        gridCrossAxisCount = 2,
        gridChildAspectRatio = 0.7,
        imageWidth = 160,
        imageHeight = 160,
        onProductTap = null,
        scrollController = null,
        externalProducts = null,
        onLoadMore = null;

  @override
  State<ProductsSection> createState() => _ProductsSectionState();
}

/// Legacy typedef for backward compatibility
typedef ProductsByCategorySection = ProductsSection;

class _ProductsSectionState extends State<ProductsSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<ProductModel> _products = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _page = 0;

  // BLoC support for category/brand products
  ProductListingBloc? _productListingBloc;
  List<ProductEntity> _blocProducts = [];
  int _currentPage = 0;
  String? _error;

  CategoryEntity? _previousCategory;

  @override
  void initState() {
    super.initState();
    _previousCategory = widget.category;

    // Initialize BLoC for category/brand products
    if (widget.type != null) {
      _productListingBloc = getIt<ProductListingBloc>();
      _loadBlocProducts();
    } else if (widget.externalProducts != null) {
      _products.addAll(widget.externalProducts!);
    } else if (widget.preloadData) {
      _loadProducts();
    }
  }

  @override
  void dispose() {
    _productListingBloc?.close();
    super.dispose();
  }

  @override
  void didUpdateWidget(ProductsSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.externalProducts != null) {
      if (oldWidget.externalProducts != widget.externalProducts ||
          (oldWidget.externalProducts?.length !=
              widget.externalProducts?.length)) {
        // Update products with new external data
        setState(() {
          _products.clear();
          _products.addAll(widget.externalProducts!);
          _page = 1; // Set to 1 since we've loaded the first page
        });
        return;
      }
    }

    if (widget.category != _previousCategory) {
      _previousCategory = widget.category;
      _page = 0;
      _products.clear();
      _loadProducts();
    }
  }

  Future<void> _loadProducts() async {
    if (_isLoading) return;
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    if (widget.externalProducts != null) {
      if (_page == 0) {
        setState(() {
          _products.clear();
          _products.addAll(widget.externalProducts!);
          _isLoading = false;
          _hasMore = widget
              .externalProducts!.isNotEmpty; // Has more if we got some products
          _page = 1; // Move to page 1 since we've loaded the first page
        });
      } else if (widget.onLoadMore != null) {
        widget.onLoadMore!();

        if (widget.externalProducts != null &&
            widget.externalProducts!.length > _products.length) {
          setState(() {
            _products.clear();
            _products.addAll(widget.externalProducts!);
          });
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final productEntities = widget.featured
          ? await _dataManager.loadFeaturedProducts(
              page: _page,
              pageSize: 10,
            )
          : await _dataManager.loadProducts(
              page: _page,
              pageSize: 12,
              category: widget.category,
              useParentId: widget.useParentId,
              useCollectionId: widget.useCollectionId,
            );

      setState(() {
        if (_page == 0) {
          _products.clear();
          _products.addAll(productEntities
              .map((entity) => ProductMapper.toModel(entity))
              .toList());
        } else {
          _products.addAll(productEntities
              .map((entity) => ProductMapper.toModel(entity))
              .toList());
        }

        _isLoading = false;
        _hasMore = productEntities.length >= 10;
        _page = _page + 1;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasMore = false;
      });
    }
  }

  // BLoC-based loading methods for category/brand products
  void _loadBlocProducts({bool refresh = false}) {
    if (widget.type == null || _productListingBloc == null) return;

    if (refresh) {
      _currentPage = 0;
      _blocProducts.clear();
      _hasMore = true;
    }

    // Load products based on type
    switch (widget.type!) {
      case ProductsSectionType.category:
        _productListingBloc!.add(
          ProductListingEvent.loadProductsByCategory(
            categoryId: widget.id!,
            page: _currentPage,
            pageSize: 12,
            refresh: refresh,
            excludeProductId: widget.excludeProductId ?? '',
          ),
        );
        break;
      case ProductsSectionType.brand:
        _productListingBloc!.add(
          ProductListingEvent.loadProductsByBrand(
            brandId: widget.id!,
            page: _currentPage,
            pageSize: 12,
            refresh: refresh,
            excludeProductId: widget.excludeProductId ?? '',
          ),
        );
        break;
    }
  }

  void _loadMoreBlocProducts() {
    if (_hasMore && !_isLoading) {
      _currentPage++;
      _loadBlocProducts();
    }
  }

  Widget _buildSeeAllHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text(
              widget.title,
              style: widget.titleStyle ??
                  Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (widget.showSeeAll && widget.onSeeAllTap != null)
            TextButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap!();
              },
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Row(
                children: [
                  Text(
                    widget.rightActionText,
                    style: widget.rightActionTextStyle ??
                        Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                  ),
                  const SizedBox(width: 2),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: widget.rightActionTextStyle?.color ??
                        Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Handle BLoC-based products (category/brand)
    if (widget.type != null && _productListingBloc != null) {
      return BlocProvider.value(
        value: _productListingBloc!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.title.isNotEmpty) _buildSeeAllHeader(),
            if (widget.title.isNotEmpty) const SizedBox(height: 12),
            BlocConsumer<ProductListingBloc, ProductListingState>(
              listener: (context, state) {
                state.when(
                  initial: () {},
                  loading: () {},
                  loaded: (category, subCategory) {},
                  error: (message) {},
                  productsByCategoryLoading: () {
                    if (widget.type == ProductsSectionType.category) {
                      setState(() {
                        _isLoading = true;
                        _error = null;
                      });
                    }
                  },
                  productsByCategoryLoaded: (products, categoryId,
                      excludeProductId, hasMore, currentPage) {
                    if (widget.type == ProductsSectionType.category &&
                        categoryId == widget.id) {
                      setState(() {
                        if (currentPage == 0) {
                          _blocProducts = List.from(products);
                        } else {
                          // Subsequent pages - append new products
                          _blocProducts.addAll(products);
                        }
                        _hasMore = hasMore;
                        _isLoading = false;
                        _error = null;
                        _currentPage =
                            currentPage; // Update current page from BLoC
                      });
                    }
                  },
                  productsByCategoryError: (message) {
                    if (widget.type == ProductsSectionType.category) {
                      setState(() {
                        _isLoading = false;
                        _error = message;
                      });
                    }
                  },
                  productsByBrandLoading: () {
                    if (widget.type == ProductsSectionType.brand) {
                      setState(() {
                        _isLoading = true;
                        _error = null;
                      });
                    }
                  },
                  productsByBrandLoaded: (products, brandId, excludeProductId,
                      hasMore, currentPage) {
                    if (widget.type == ProductsSectionType.brand &&
                        brandId == widget.id) {
                      setState(() {
                        if (currentPage == 0) {
                          // First page - replace all products
                          _blocProducts = List.from(products);
                        } else {
                          // Subsequent pages - append new products
                          _blocProducts.addAll(products);
                        }
                        _hasMore = hasMore;
                        _isLoading = false;
                        _error = null;
                        _currentPage =
                            currentPage; // Update current page from BLoC
                      });
                    }
                  },
                  productsByBrandError: (message) {
                    if (widget.type == ProductsSectionType.brand) {
                      setState(() {
                        _isLoading = false;
                        _error = message;
                      });
                    }
                  },
                  productBySkuLoading: () {},
                  productBySkuLoaded: (product) {},
                  productBySkuError: (message) {},
                );
              },
              builder: (context, state) {
                if (_error != null) {
                  return _buildErrorWidget();
                }

                if (_blocProducts.isEmpty && _isLoading) {
                  return _buildLoadingWidget();
                }

                if (_blocProducts.isEmpty && !_isLoading) {
                  return _buildEmptyWidget();
                }

                return _buildBlocProductList();
              },
            ),
          ],
        ),
      );
    }

    // Handle regular products (DataLoadingManager-based)
    if (widget.useGridView && widget.scrollController != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showSeeAll) _buildSeeAllHeader(),
          if (widget.showSeeAll) const SizedBox(height: 12),
          Expanded(
            child: _buildProductGrid(),
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showSeeAll)
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildSeeAllHeader(),
            ),
          // const SizedBox(height: 12),
          widget.useGridView ? _buildProductGrid() : _buildProductList(),
        ],
      );
    }
  }

  Widget _buildProductList() {
    if (_isLoading && _products.isEmpty) {
      return Padding(
        padding: const EdgeInsets.only(top: 16),
        child: ProductSkeletonLoader(
          useGridView: false,
          showAsRow: false,
          itemCount: 6,
        ),
      );
    }
    return HorizontalLazyLoadingList<ProductModel>(
      height: widget.height,
      items: _products,
      isLoading: _isLoading,
      hasMoreData: _hasMore,
      onLoadMore: _hasMore ? _loadProducts : null,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      showEmptyText: widget.showEmptyText,
      itemBuilder: (context, product, index) {
        return Padding(
            padding: EdgeInsets.only(
              right: index == _products.length - 1 ? 0 : 16,
              top: widget.topPadding ?? 0,
            ),
            child: SizedBox(
              width: widget.cardWidth,
              child: DiscountedProductCard(
                product: ProductMapper.toEntity(product),
                isLoading: false,
              ),
            )
            // _buildProductCard(product, 160, widget.height - 16),
            );
      },
    );
  }

  Widget _buildProductGrid() {
    if (_isLoading && _products.isEmpty) {
      // return ProductSkeletonLoader(
      //   useGridView: true,
      //   showAsRow: false,
      //   gridCrossAxisCount: widget.gridCrossAxisCount,
      //   gridChildAspectRatio: widget.gridChildAspectRatio,
      //   itemCount: 6,
      // );
      return LayoutBuilder(builder: (context, constraints) {
        return GridView.builder(
          shrinkWrap: widget.shrinkWrap ?? true,
          padding: EdgeInsets.only(
              left: 16, right: 16, bottom: widget.bottomPadding),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: widget.gridCrossAxisCount,
            crossAxisSpacing: constraints.maxWidth * 0.025,
            mainAxisSpacing: constraints.maxWidth * 0.025,
            mainAxisExtent: constraints.maxWidth /
                (constraints.maxWidth * (widget.gridChildAspectRatio)),
          ),
          itemBuilder: (ctx, index) {
            return ProductCardShimmer();
          },
          itemCount: 6,
        );
      });
    }
    return LayoutBuilder(builder: (context, constraints) {
      return GridLazyLoadingWidget<ProductModel>(
        // primary: widget.primary,
        items: _products,
        isLoading: _isLoading,
        hasMoreData: _hasMore,
        onLoadMore: _hasMore ? _loadProducts : null,
        padding:
            EdgeInsets.only(left: 16, right: 16, bottom: widget.bottomPadding),
        scrollController: widget.scrollController,
        // Always use scrollable physics for grid view in product listing
        physics: widget.physics ??
            (widget.useGridView
                ? const AlwaysScrollableScrollPhysics()
                : (widget.scrollController != null
                    ? const AlwaysScrollableScrollPhysics()
                    : null)),
        // Only shrink wrap for horizontal lists, not for grid views
        shrinkWrap: widget.shrinkWrap ?? !widget.useGridView,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.gridCrossAxisCount,
          // childAspectRatio:
          // widget.gridChildAspectRatio,
          crossAxisSpacing: constraints.maxWidth * 0.025,
          mainAxisSpacing: constraints.maxWidth * 0.025,
          mainAxisExtent: constraints.maxWidth /
              (constraints.maxWidth * (widget.gridChildAspectRatio)),
        ),
        itemBuilder: (context, product, index) {
          return DiscountedProductCard(
            product: ProductMapper.toEntity(product),
            isLoading: false,
          );
          // _buildProductCard(product, cardWidth, cardHeight);
        },
      );
    });
  }

  // BLoC-specific widget builders
  Widget _buildBlocProductList() {
    return HorizontalLazyLoadingList<ProductEntity>(
      height: widget.height,
      items: _blocProducts,
      isLoading: _isLoading,
      hasMoreData: _hasMore,
      onLoadMore: _hasMore ? _loadMoreBlocProducts : null,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, product, index) {
        return Padding(
          padding: EdgeInsets.only(
            right: index == _blocProducts.length - 1 ? 0 : 16,
          ),
          child: SizedBox(
            width: 160,
            child: DiscountedProductCard(
              product: product,
              isLoading: false,
              height: widget.height - 16,
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingWidget() {
    return SizedBox(
      height: widget.height,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 6,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(right: index == 5 ? 0 : 16),
            child: SizedBox(
              width: 160,
              child: ShimmerBox(
                height: widget.height - 16,
                width: 160,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyWidget() {
    final String emptyMessage = widget.type == ProductsSectionType.category
        ? 'No products found in this category'
        : 'No products found for this brand';

    return SizedBox(
      height: widget.height,
      child: Center(
        child: Text(emptyMessage),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return SizedBox(
      height: widget.height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error loading products',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => _loadBlocProducts(refresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
