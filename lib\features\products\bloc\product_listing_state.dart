import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/category_entity.dart';
import '../../../domain/entities/product_entity.dart';

part 'product_listing_state.freezed.dart';

@freezed
class ProductListingState with _$ProductListingState {
  const factory ProductListingState.initial() = _Initial;
  const factory ProductListingState.loading() = _Loading;

  const factory ProductListingState.loaded({
    CategoryEntity? category,
    CategoryEntity? subCategory,
  }) = _Loaded;

  const factory ProductListingState.error(String message) = _Error;

  // Products by category states
  const factory ProductListingState.productsByCategoryLoading() = _ProductsByCategoryLoading;

  const factory ProductListingState.productsByCategoryLoaded({
    required List<ProductEntity> products,
    required String categoryId,
    required String excludeProductId,
    required bool hasMore,
    required int currentPage,
  }) = _ProductsByCategoryLoaded;

  const factory ProductListingState.productsByCategoryError(String message) = _ProductsByCategoryError;

  // Products by brand states
  const factory ProductListingState.productsByBrandLoading() = _ProductsByBrandLoading;

  const factory ProductListingState.productsByBrandLoaded({
    required List<ProductEntity> products,
    required String brandId,
    required String excludeProductId,
    required bool hasMore,
    required int currentPage,
    
  }) = _ProductsByBrandLoaded;

  const factory ProductListingState.productsByBrandError(String message) = _ProductsByBrandError;
  
  // Product detail by SKU states
  const factory ProductListingState.productBySkuLoading() = _ProductBySkuLoading;
  
  const factory ProductListingState.productBySkuLoaded({
    required ProductEntity product,
  }) = _ProductBySkuLoaded;
  
  const factory ProductListingState.productBySkuError(String message) = _ProductBySkuError;
}
