// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_listing_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductListingState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ProductListingState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingState()';
  }
}

/// @nodoc
class $ProductListingStateCopyWith<$Res> {
  $ProductListingStateCopyWith(
      ProductListingState _, $Res Function(ProductListingState) __);
}

/// Adds pattern-matching-related methods to [ProductListingState].
extension ProductListingStatePatterns on ProductListingState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_ProductsByCategoryLoading value)?
        productsByCategoryLoading,
    TResult Function(_ProductsByCategoryLoaded value)? productsByCategoryLoaded,
    TResult Function(_ProductsByCategoryError value)? productsByCategoryError,
    TResult Function(_ProductsByBrandLoading value)? productsByBrandLoading,
    TResult Function(_ProductsByBrandLoaded value)? productsByBrandLoaded,
    TResult Function(_ProductsByBrandError value)? productsByBrandError,
    TResult Function(_ProductBySkuLoading value)? productBySkuLoading,
    TResult Function(_ProductBySkuLoaded value)? productBySkuLoaded,
    TResult Function(_ProductBySkuError value)? productBySkuError,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Error() when error != null:
        return error(_that);
      case _ProductsByCategoryLoading() when productsByCategoryLoading != null:
        return productsByCategoryLoading(_that);
      case _ProductsByCategoryLoaded() when productsByCategoryLoaded != null:
        return productsByCategoryLoaded(_that);
      case _ProductsByCategoryError() when productsByCategoryError != null:
        return productsByCategoryError(_that);
      case _ProductsByBrandLoading() when productsByBrandLoading != null:
        return productsByBrandLoading(_that);
      case _ProductsByBrandLoaded() when productsByBrandLoaded != null:
        return productsByBrandLoaded(_that);
      case _ProductsByBrandError() when productsByBrandError != null:
        return productsByBrandError(_that);
      case _ProductBySkuLoading() when productBySkuLoading != null:
        return productBySkuLoading(_that);
      case _ProductBySkuLoaded() when productBySkuLoaded != null:
        return productBySkuLoaded(_that);
      case _ProductBySkuError() when productBySkuError != null:
        return productBySkuError(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_ProductsByCategoryLoading value)
        productsByCategoryLoading,
    required TResult Function(_ProductsByCategoryLoaded value)
        productsByCategoryLoaded,
    required TResult Function(_ProductsByCategoryError value)
        productsByCategoryError,
    required TResult Function(_ProductsByBrandLoading value)
        productsByBrandLoading,
    required TResult Function(_ProductsByBrandLoaded value)
        productsByBrandLoaded,
    required TResult Function(_ProductsByBrandError value) productsByBrandError,
    required TResult Function(_ProductBySkuLoading value) productBySkuLoading,
    required TResult Function(_ProductBySkuLoaded value) productBySkuLoaded,
    required TResult Function(_ProductBySkuError value) productBySkuError,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Loaded():
        return loaded(_that);
      case _Error():
        return error(_that);
      case _ProductsByCategoryLoading():
        return productsByCategoryLoading(_that);
      case _ProductsByCategoryLoaded():
        return productsByCategoryLoaded(_that);
      case _ProductsByCategoryError():
        return productsByCategoryError(_that);
      case _ProductsByBrandLoading():
        return productsByBrandLoading(_that);
      case _ProductsByBrandLoaded():
        return productsByBrandLoaded(_that);
      case _ProductsByBrandError():
        return productsByBrandError(_that);
      case _ProductBySkuLoading():
        return productBySkuLoading(_that);
      case _ProductBySkuLoaded():
        return productBySkuLoaded(_that);
      case _ProductBySkuError():
        return productBySkuError(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_ProductsByCategoryLoading value)?
        productsByCategoryLoading,
    TResult? Function(_ProductsByCategoryLoaded value)?
        productsByCategoryLoaded,
    TResult? Function(_ProductsByCategoryError value)? productsByCategoryError,
    TResult? Function(_ProductsByBrandLoading value)? productsByBrandLoading,
    TResult? Function(_ProductsByBrandLoaded value)? productsByBrandLoaded,
    TResult? Function(_ProductsByBrandError value)? productsByBrandError,
    TResult? Function(_ProductBySkuLoading value)? productBySkuLoading,
    TResult? Function(_ProductBySkuLoaded value)? productBySkuLoaded,
    TResult? Function(_ProductBySkuError value)? productBySkuError,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Error() when error != null:
        return error(_that);
      case _ProductsByCategoryLoading() when productsByCategoryLoading != null:
        return productsByCategoryLoading(_that);
      case _ProductsByCategoryLoaded() when productsByCategoryLoaded != null:
        return productsByCategoryLoaded(_that);
      case _ProductsByCategoryError() when productsByCategoryError != null:
        return productsByCategoryError(_that);
      case _ProductsByBrandLoading() when productsByBrandLoading != null:
        return productsByBrandLoading(_that);
      case _ProductsByBrandLoaded() when productsByBrandLoaded != null:
        return productsByBrandLoaded(_that);
      case _ProductsByBrandError() when productsByBrandError != null:
        return productsByBrandError(_that);
      case _ProductBySkuLoading() when productBySkuLoading != null:
        return productBySkuLoading(_that);
      case _ProductBySkuLoaded() when productBySkuLoaded != null:
        return productBySkuLoaded(_that);
      case _ProductBySkuError() when productBySkuError != null:
        return productBySkuError(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(CategoryEntity? category, CategoryEntity? subCategory)?
        loaded,
    TResult Function(String message)? error,
    TResult Function()? productsByCategoryLoading,
    TResult Function(List<ProductEntity> products, String categoryId,
            String excludeProductId, bool hasMore, int currentPage)?
        productsByCategoryLoaded,
    TResult Function(String message)? productsByCategoryError,
    TResult Function()? productsByBrandLoading,
    TResult Function(List<ProductEntity> products, String brandId,
            String excludeProductId, bool hasMore, int currentPage)?
        productsByBrandLoaded,
    TResult Function(String message)? productsByBrandError,
    TResult Function()? productBySkuLoading,
    TResult Function(ProductEntity product)? productBySkuLoaded,
    TResult Function(String message)? productBySkuError,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Loaded() when loaded != null:
        return loaded(_that.category, _that.subCategory);
      case _Error() when error != null:
        return error(_that.message);
      case _ProductsByCategoryLoading() when productsByCategoryLoading != null:
        return productsByCategoryLoading();
      case _ProductsByCategoryLoaded() when productsByCategoryLoaded != null:
        return productsByCategoryLoaded(_that.products, _that.categoryId,
            _that.excludeProductId, _that.hasMore, _that.currentPage);
      case _ProductsByCategoryError() when productsByCategoryError != null:
        return productsByCategoryError(_that.message);
      case _ProductsByBrandLoading() when productsByBrandLoading != null:
        return productsByBrandLoading();
      case _ProductsByBrandLoaded() when productsByBrandLoaded != null:
        return productsByBrandLoaded(_that.products, _that.brandId,
            _that.excludeProductId, _that.hasMore, _that.currentPage);
      case _ProductsByBrandError() when productsByBrandError != null:
        return productsByBrandError(_that.message);
      case _ProductBySkuLoading() when productBySkuLoading != null:
        return productBySkuLoading();
      case _ProductBySkuLoaded() when productBySkuLoaded != null:
        return productBySkuLoaded(_that.product);
      case _ProductBySkuError() when productBySkuError != null:
        return productBySkuError(_that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            CategoryEntity? category, CategoryEntity? subCategory)
        loaded,
    required TResult Function(String message) error,
    required TResult Function() productsByCategoryLoading,
    required TResult Function(List<ProductEntity> products, String categoryId,
            String excludeProductId, bool hasMore, int currentPage)
        productsByCategoryLoaded,
    required TResult Function(String message) productsByCategoryError,
    required TResult Function() productsByBrandLoading,
    required TResult Function(List<ProductEntity> products, String brandId,
            String excludeProductId, bool hasMore, int currentPage)
        productsByBrandLoaded,
    required TResult Function(String message) productsByBrandError,
    required TResult Function() productBySkuLoading,
    required TResult Function(ProductEntity product) productBySkuLoaded,
    required TResult Function(String message) productBySkuError,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _Loaded():
        return loaded(_that.category, _that.subCategory);
      case _Error():
        return error(_that.message);
      case _ProductsByCategoryLoading():
        return productsByCategoryLoading();
      case _ProductsByCategoryLoaded():
        return productsByCategoryLoaded(_that.products, _that.categoryId,
            _that.excludeProductId, _that.hasMore, _that.currentPage);
      case _ProductsByCategoryError():
        return productsByCategoryError(_that.message);
      case _ProductsByBrandLoading():
        return productsByBrandLoading();
      case _ProductsByBrandLoaded():
        return productsByBrandLoaded(_that.products, _that.brandId,
            _that.excludeProductId, _that.hasMore, _that.currentPage);
      case _ProductsByBrandError():
        return productsByBrandError(_that.message);
      case _ProductBySkuLoading():
        return productBySkuLoading();
      case _ProductBySkuLoaded():
        return productBySkuLoaded(_that.product);
      case _ProductBySkuError():
        return productBySkuError(_that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(CategoryEntity? category, CategoryEntity? subCategory)?
        loaded,
    TResult? Function(String message)? error,
    TResult? Function()? productsByCategoryLoading,
    TResult? Function(List<ProductEntity> products, String categoryId,
            String excludeProductId, bool hasMore, int currentPage)?
        productsByCategoryLoaded,
    TResult? Function(String message)? productsByCategoryError,
    TResult? Function()? productsByBrandLoading,
    TResult? Function(List<ProductEntity> products, String brandId,
            String excludeProductId, bool hasMore, int currentPage)?
        productsByBrandLoaded,
    TResult? Function(String message)? productsByBrandError,
    TResult? Function()? productBySkuLoading,
    TResult? Function(ProductEntity product)? productBySkuLoaded,
    TResult? Function(String message)? productBySkuError,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Loaded() when loaded != null:
        return loaded(_that.category, _that.subCategory);
      case _Error() when error != null:
        return error(_that.message);
      case _ProductsByCategoryLoading() when productsByCategoryLoading != null:
        return productsByCategoryLoading();
      case _ProductsByCategoryLoaded() when productsByCategoryLoaded != null:
        return productsByCategoryLoaded(_that.products, _that.categoryId,
            _that.excludeProductId, _that.hasMore, _that.currentPage);
      case _ProductsByCategoryError() when productsByCategoryError != null:
        return productsByCategoryError(_that.message);
      case _ProductsByBrandLoading() when productsByBrandLoading != null:
        return productsByBrandLoading();
      case _ProductsByBrandLoaded() when productsByBrandLoaded != null:
        return productsByBrandLoaded(_that.products, _that.brandId,
            _that.excludeProductId, _that.hasMore, _that.currentPage);
      case _ProductsByBrandError() when productsByBrandError != null:
        return productsByBrandError(_that.message);
      case _ProductBySkuLoading() when productBySkuLoading != null:
        return productBySkuLoading();
      case _ProductBySkuLoaded() when productBySkuLoaded != null:
        return productBySkuLoaded(_that.product);
      case _ProductBySkuError() when productBySkuError != null:
        return productBySkuError(_that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements ProductListingState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingState.initial()';
  }
}

/// @nodoc

class _Loading implements ProductListingState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingState.loading()';
  }
}

/// @nodoc

class _Loaded implements ProductListingState {
  const _Loaded({this.category, this.subCategory});

  final CategoryEntity? category;
  final CategoryEntity? subCategory;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadedCopyWith<_Loaded> get copyWith =>
      __$LoadedCopyWithImpl<_Loaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loaded &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.subCategory, subCategory) ||
                other.subCategory == subCategory));
  }

  @override
  int get hashCode => Object.hash(runtimeType, category, subCategory);

  @override
  String toString() {
    return 'ProductListingState.loaded(category: $category, subCategory: $subCategory)';
  }
}

/// @nodoc
abstract mixin class _$LoadedCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$LoadedCopyWith(_Loaded value, $Res Function(_Loaded) _then) =
      __$LoadedCopyWithImpl;
  @useResult
  $Res call({CategoryEntity? category, CategoryEntity? subCategory});
}

/// @nodoc
class __$LoadedCopyWithImpl<$Res> implements _$LoadedCopyWith<$Res> {
  __$LoadedCopyWithImpl(this._self, this._then);

  final _Loaded _self;
  final $Res Function(_Loaded) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? category = freezed,
    Object? subCategory = freezed,
  }) {
    return _then(_Loaded(
      category: freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
      subCategory: freezed == subCategory
          ? _self.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
    ));
  }
}

/// @nodoc

class _Error implements ProductListingState {
  const _Error(this.message);

  final String message;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ErrorCopyWith<_Error> get copyWith =>
      __$ErrorCopyWithImpl<_Error>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Error &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'ProductListingState.error(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) =
      __$ErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$ErrorCopyWithImpl<$Res> implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_Error(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _ProductsByCategoryLoading implements ProductListingState {
  const _ProductsByCategoryLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsByCategoryLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingState.productsByCategoryLoading()';
  }
}

/// @nodoc

class _ProductsByCategoryLoaded implements ProductListingState {
  const _ProductsByCategoryLoaded(
      {required final List<ProductEntity> products,
      required this.categoryId,
      required this.excludeProductId,
      required this.hasMore,
      required this.currentPage})
      : _products = products;

  final List<ProductEntity> _products;
  List<ProductEntity> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final String categoryId;
  final String excludeProductId;
  final bool hasMore;
  final int currentPage;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsByCategoryLoadedCopyWith<_ProductsByCategoryLoaded> get copyWith =>
      __$ProductsByCategoryLoadedCopyWithImpl<_ProductsByCategoryLoaded>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsByCategoryLoaded &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.excludeProductId, excludeProductId) ||
                other.excludeProductId == excludeProductId) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_products),
      categoryId,
      excludeProductId,
      hasMore,
      currentPage);

  @override
  String toString() {
    return 'ProductListingState.productsByCategoryLoaded(products: $products, categoryId: $categoryId, excludeProductId: $excludeProductId, hasMore: $hasMore, currentPage: $currentPage)';
  }
}

/// @nodoc
abstract mixin class _$ProductsByCategoryLoadedCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ProductsByCategoryLoadedCopyWith(_ProductsByCategoryLoaded value,
          $Res Function(_ProductsByCategoryLoaded) _then) =
      __$ProductsByCategoryLoadedCopyWithImpl;
  @useResult
  $Res call(
      {List<ProductEntity> products,
      String categoryId,
      String excludeProductId,
      bool hasMore,
      int currentPage});
}

/// @nodoc
class __$ProductsByCategoryLoadedCopyWithImpl<$Res>
    implements _$ProductsByCategoryLoadedCopyWith<$Res> {
  __$ProductsByCategoryLoadedCopyWithImpl(this._self, this._then);

  final _ProductsByCategoryLoaded _self;
  final $Res Function(_ProductsByCategoryLoaded) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? products = null,
    Object? categoryId = null,
    Object? excludeProductId = null,
    Object? hasMore = null,
    Object? currentPage = null,
  }) {
    return _then(_ProductsByCategoryLoaded(
      products: null == products
          ? _self._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>,
      categoryId: null == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      excludeProductId: null == excludeProductId
          ? _self.excludeProductId
          : excludeProductId // ignore: cast_nullable_to_non_nullable
              as String,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _ProductsByCategoryError implements ProductListingState {
  const _ProductsByCategoryError(this.message);

  final String message;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsByCategoryErrorCopyWith<_ProductsByCategoryError> get copyWith =>
      __$ProductsByCategoryErrorCopyWithImpl<_ProductsByCategoryError>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsByCategoryError &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'ProductListingState.productsByCategoryError(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$ProductsByCategoryErrorCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ProductsByCategoryErrorCopyWith(_ProductsByCategoryError value,
          $Res Function(_ProductsByCategoryError) _then) =
      __$ProductsByCategoryErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$ProductsByCategoryErrorCopyWithImpl<$Res>
    implements _$ProductsByCategoryErrorCopyWith<$Res> {
  __$ProductsByCategoryErrorCopyWithImpl(this._self, this._then);

  final _ProductsByCategoryError _self;
  final $Res Function(_ProductsByCategoryError) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_ProductsByCategoryError(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _ProductsByBrandLoading implements ProductListingState {
  const _ProductsByBrandLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ProductsByBrandLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingState.productsByBrandLoading()';
  }
}

/// @nodoc

class _ProductsByBrandLoaded implements ProductListingState {
  const _ProductsByBrandLoaded(
      {required final List<ProductEntity> products,
      required this.brandId,
      required this.excludeProductId,
      required this.hasMore,
      required this.currentPage})
      : _products = products;

  final List<ProductEntity> _products;
  List<ProductEntity> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final String brandId;
  final String excludeProductId;
  final bool hasMore;
  final int currentPage;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsByBrandLoadedCopyWith<_ProductsByBrandLoaded> get copyWith =>
      __$ProductsByBrandLoadedCopyWithImpl<_ProductsByBrandLoaded>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsByBrandLoaded &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            (identical(other.brandId, brandId) || other.brandId == brandId) &&
            (identical(other.excludeProductId, excludeProductId) ||
                other.excludeProductId == excludeProductId) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_products),
      brandId,
      excludeProductId,
      hasMore,
      currentPage);

  @override
  String toString() {
    return 'ProductListingState.productsByBrandLoaded(products: $products, brandId: $brandId, excludeProductId: $excludeProductId, hasMore: $hasMore, currentPage: $currentPage)';
  }
}

/// @nodoc
abstract mixin class _$ProductsByBrandLoadedCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ProductsByBrandLoadedCopyWith(_ProductsByBrandLoaded value,
          $Res Function(_ProductsByBrandLoaded) _then) =
      __$ProductsByBrandLoadedCopyWithImpl;
  @useResult
  $Res call(
      {List<ProductEntity> products,
      String brandId,
      String excludeProductId,
      bool hasMore,
      int currentPage});
}

/// @nodoc
class __$ProductsByBrandLoadedCopyWithImpl<$Res>
    implements _$ProductsByBrandLoadedCopyWith<$Res> {
  __$ProductsByBrandLoadedCopyWithImpl(this._self, this._then);

  final _ProductsByBrandLoaded _self;
  final $Res Function(_ProductsByBrandLoaded) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? products = null,
    Object? brandId = null,
    Object? excludeProductId = null,
    Object? hasMore = null,
    Object? currentPage = null,
  }) {
    return _then(_ProductsByBrandLoaded(
      products: null == products
          ? _self._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>,
      brandId: null == brandId
          ? _self.brandId
          : brandId // ignore: cast_nullable_to_non_nullable
              as String,
      excludeProductId: null == excludeProductId
          ? _self.excludeProductId
          : excludeProductId // ignore: cast_nullable_to_non_nullable
              as String,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _ProductsByBrandError implements ProductListingState {
  const _ProductsByBrandError(this.message);

  final String message;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsByBrandErrorCopyWith<_ProductsByBrandError> get copyWith =>
      __$ProductsByBrandErrorCopyWithImpl<_ProductsByBrandError>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsByBrandError &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'ProductListingState.productsByBrandError(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$ProductsByBrandErrorCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ProductsByBrandErrorCopyWith(_ProductsByBrandError value,
          $Res Function(_ProductsByBrandError) _then) =
      __$ProductsByBrandErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$ProductsByBrandErrorCopyWithImpl<$Res>
    implements _$ProductsByBrandErrorCopyWith<$Res> {
  __$ProductsByBrandErrorCopyWithImpl(this._self, this._then);

  final _ProductsByBrandError _self;
  final $Res Function(_ProductsByBrandError) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_ProductsByBrandError(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _ProductBySkuLoading implements ProductListingState {
  const _ProductBySkuLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ProductBySkuLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingState.productBySkuLoading()';
  }
}

/// @nodoc

class _ProductBySkuLoaded implements ProductListingState {
  const _ProductBySkuLoaded({required this.product});

  final ProductEntity product;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductBySkuLoadedCopyWith<_ProductBySkuLoaded> get copyWith =>
      __$ProductBySkuLoadedCopyWithImpl<_ProductBySkuLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductBySkuLoaded &&
            (identical(other.product, product) || other.product == product));
  }

  @override
  int get hashCode => Object.hash(runtimeType, product);

  @override
  String toString() {
    return 'ProductListingState.productBySkuLoaded(product: $product)';
  }
}

/// @nodoc
abstract mixin class _$ProductBySkuLoadedCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ProductBySkuLoadedCopyWith(
          _ProductBySkuLoaded value, $Res Function(_ProductBySkuLoaded) _then) =
      __$ProductBySkuLoadedCopyWithImpl;
  @useResult
  $Res call({ProductEntity product});
}

/// @nodoc
class __$ProductBySkuLoadedCopyWithImpl<$Res>
    implements _$ProductBySkuLoadedCopyWith<$Res> {
  __$ProductBySkuLoadedCopyWithImpl(this._self, this._then);

  final _ProductBySkuLoaded _self;
  final $Res Function(_ProductBySkuLoaded) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? product = null,
  }) {
    return _then(_ProductBySkuLoaded(
      product: null == product
          ? _self.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductEntity,
    ));
  }
}

/// @nodoc

class _ProductBySkuError implements ProductListingState {
  const _ProductBySkuError(this.message);

  final String message;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductBySkuErrorCopyWith<_ProductBySkuError> get copyWith =>
      __$ProductBySkuErrorCopyWithImpl<_ProductBySkuError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductBySkuError &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'ProductListingState.productBySkuError(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$ProductBySkuErrorCopyWith<$Res>
    implements $ProductListingStateCopyWith<$Res> {
  factory _$ProductBySkuErrorCopyWith(
          _ProductBySkuError value, $Res Function(_ProductBySkuError) _then) =
      __$ProductBySkuErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$ProductBySkuErrorCopyWithImpl<$Res>
    implements _$ProductBySkuErrorCopyWith<$Res> {
  __$ProductBySkuErrorCopyWithImpl(this._self, this._then);

  final _ProductBySkuError _self;
  final $Res Function(_ProductBySkuError) _then;

  /// Create a copy of ProductListingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_ProductBySkuError(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
