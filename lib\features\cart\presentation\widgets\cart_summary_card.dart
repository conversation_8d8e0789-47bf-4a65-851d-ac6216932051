import 'package:flutter/material.dart';
import 'package:rozana/data/models/cart_model.dart';
import 'package:rozana/core/themes/color_schemes.dart';

class CartSummaryCard extends StatelessWidget {
  final CartModel cart;

  const CartSummaryCard({
    super.key,
    required this.cart,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.textHint),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Price Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.neutral600,
              ),
            ),
            const SizedBox(height: 16),

            // Subtotal
            _buildPriceRow(
              label: 'Subtotal',
              value: '₹${cart.subTotal?.toStringAsFixed(2)}',
            ),
            const SizedBox(height: 8),

            // Delivery fee
            _buildPriceRow(
              label: 'Delivery Fee',
              value: (cart.deliveryFee ?? 0) > 0
                  ? '₹${cart.deliveryFee?.toStringAsFixed(2)}'
                  : 'FREE',
              valueColor:
                  (cart.deliveryFee ?? 0) > 0 ? null : AppColors.success,
            ),
            const SizedBox(height: 8),

            // Tax
            _buildPriceRow(
              label: 'Tax',
              value: '₹${(cart.tax ?? 0).toStringAsFixed(2)}',
            ),

            // Discount (if applicable)
            if ((cart.discount ?? 0) > 0) ...[
              const SizedBox(height: 8),
              _buildPriceRow(
                label: 'Discount',
                value: '-₹${cart.discount?.toStringAsFixed(2)}',
                valueColor: AppColors.success,
              ),
            ],

            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Divider(),
            ),

            // Total
            _buildPriceRow(
              label: 'Total',
              value: '₹${cart.total?.toStringAsFixed(2)}',
              isTotal: true,
            ),

            // Savings (if applicable)
            if ((cart.discount ?? 0) > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.savings_outlined,
                      size: 16,
                      color: AppColors.success,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'You saved ₹${cart.discount?.toStringAsFixed(2)} on this order',
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRow({
    required String label,
    required String value,
    Color? valueColor,
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 15 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            color: AppColors.neutral600,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            color: valueColor ?? AppColors.neutral600,
          ),
        ),
      ],
    );
  }
}
